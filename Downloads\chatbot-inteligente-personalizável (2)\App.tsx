
import React, { useState, useEffect, useRef } from 'react';
import { Part } from "@google/genai";
// PDF.js is now loaded globally via a script tag in index.html

import { ChatMessage, KnowledgeItem, KnowledgeItemType, GroundingChunk } from './types';
import { GEMINI_CHAT_MODEL, DEFAULT_SYSTEM_INSTRUCTION, MAX_KNOWLEDGE_CONTEXT_LENGTH } from './constants';
import { sendMessageToGemini, initializeChat, textToSpeech, convertAppMessagesToGeminiHistory } from './services/geminiService';
import ChatMessageItem from './components/ChatMessage';
import KnowledgeItemCard from './components/KnowledgeItemCard';
import MicIcon from './components/icons/MicIcon';
import SendIcon from './components/icons/SendIcon';
import SpeakerIcon from './components/icons/SpeakerIcon';
import SpinnerIcon from './components/icons/SpinnerIcon';
import PlusIcon from './components/icons/PlusIcon';

const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
let recognition: any;
if (SpeechRecognition) {
  recognition = new SpeechRecognition();
  recognition.continuous = false;
  recognition.lang = 'pt-BR';
  recognition.interimResults = false;
  recognition.maxAlternatives = 1;
} else {
  console.warn("Speech Recognition API não é suportada neste navegador.");
}

// Helper function to initialize PDF.js worker if needed
const ensurePdfJsWorkerInitialized = (): boolean => {
  const pdfjsLib = (window as any).pdfjsLib;
  if (pdfjsLib && typeof pdfjsLib.getDocument === 'function') {
    if (pdfjsLib.GlobalWorkerOptions) {
      if (typeof window !== 'undefined' && !pdfjsLib.GlobalWorkerOptions.workerSrc) { // Ensure runs in browser & not already set
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.js';
        console.log('[App.tsx] PDF.js workerSrc set by ensurePdfJsWorkerInitialized to jsDelivr.');
      }
      return true; // Library and GlobalWorkerOptions are available
    } else {
      console.warn('[App.tsx] PDF.js: pdfjsLib.GlobalWorkerOptions is not available. PDF processing will likely fail as worker cannot be configured.');
      return false; // GlobalWorkerOptions needed for worker setup
    }
  }
  if (!pdfjsLib) console.log('[App.tsx] ensurePdfJsWorkerInitialized: window.pdfjsLib not found.');
  else if (typeof pdfjsLib.getDocument !== 'function') console.log('[App.tsx] ensurePdfJsWorkerInitialized: window.pdfjsLib.getDocument is not a function.');
  return false; // PDF.js library not fully ready
};


const App: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [audioOutputEnabled, setAudioOutputEnabled] = useState<boolean>(false);
  
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([]);
  const [newKnowledgeName, setNewKnowledgeName] = useState<string>('');
  const [newKnowledgeContent, setNewKnowledgeContent] = useState<string>(''); // Used for text, web pasted content, URL, and audio description
  const [newKnowledgeFile, setNewKnowledgeFile] = useState<File | null>(null);
  const [newKnowledgeType, setNewKnowledgeType] = useState<KnowledgeItemType>(KnowledgeItemType.TEXT);
  const [showKnowledgeForm, setShowKnowledgeForm] = useState<boolean>(false);
  const [selectedKnowledgeIds, setSelectedKnowledgeIds] = useState<Set<string>>(new Set());
  const [isFileProcessing, setIsFileProcessing] = useState<boolean>(false);

  const [systemInstruction, setSystemInstruction] = useState<string>(DEFAULT_SYSTEM_INSTRUCTION);
  const [currentSystemInstructionInChat, setCurrentSystemInstructionInChat] = useState<string>(DEFAULT_SYSTEM_INSTRUCTION);

  const [pdfLibraryAvailable, setPdfLibraryAvailable] = useState<boolean>(false);

  const chatMessagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const lib = (window as any).pdfjsLib;
    if (lib && typeof lib.getDocument === 'function' && lib.GlobalWorkerOptions) {
      setPdfLibraryAvailable(true);
      return; 
    }
    let attempts = 0;
    const maxAttempts = 20; 
    const intervalId = setInterval(() => {
      attempts++;
      const currentLib = (window as any).pdfjsLib;
      if (currentLib && typeof currentLib.getDocument === 'function' && currentLib.GlobalWorkerOptions) {
        setPdfLibraryAvailable(true);
        clearInterval(intervalId);
      } else if (attempts >= maxAttempts) {
        clearInterval(intervalId);
      }
    }, 500);
    return () => clearInterval(intervalId);
  }, []);


  useEffect(() => {
    const storedMessages = localStorage.getItem('chatMessages');
    if (storedMessages) setMessages(JSON.parse(storedMessages));

    const storedKnowledge = localStorage.getItem('knowledgeItems');
    if (storedKnowledge) setKnowledgeItems(JSON.parse(storedKnowledge));
    
    const storedSelectedKnowledgeIds = localStorage.getItem('selectedKnowledgeIds');
    if (storedSelectedKnowledgeIds) setSelectedKnowledgeIds(new Set(JSON.parse(storedSelectedKnowledgeIds)));

    const storedSystemInstruction = localStorage.getItem('systemInstruction');
    if (storedSystemInstruction) {
      setSystemInstruction(storedSystemInstruction);
      setCurrentSystemInstructionInChat(storedSystemInstruction); 
    }
    
    const storedAudioOutput = localStorage.getItem('audioOutputEnabled');
    if (storedAudioOutput) setAudioOutputEnabled(JSON.parse(storedAudioOutput));

    const geminiHistory = convertAppMessagesToGeminiHistory(storedMessages ? JSON.parse(storedMessages) : []);
    initializeChat(storedSystemInstruction || DEFAULT_SYSTEM_INSTRUCTION, geminiHistory);
  }, []);

  useEffect(() => { localStorage.setItem('chatMessages', JSON.stringify(messages)); }, [messages]);
  useEffect(() => { localStorage.setItem('knowledgeItems', JSON.stringify(knowledgeItems)); }, [knowledgeItems]);
  useEffect(() => { localStorage.setItem('selectedKnowledgeIds', JSON.stringify(Array.from(selectedKnowledgeIds)));}, [selectedKnowledgeIds]);
  useEffect(() => { localStorage.setItem('systemInstruction', systemInstruction); }, [systemInstruction]);
  useEffect(() => { localStorage.setItem('audioOutputEnabled', JSON.stringify(audioOutputEnabled));}, [audioOutputEnabled]);
  useEffect(() => { chatMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }, [messages]);

  useEffect(() => {
    if (recognition) {
      recognition.onresult = (event: any) => {
        setInputText(event.results[0][0].transcript);
        setIsRecording(false);
      };
      recognition.onerror = (event: any) => {
        console.error("Erro no reconhecimento de voz:", event.error);
        addMessageToChat(`Erro no reconhecimento de voz: ${event.error}`, 'system');
        setIsRecording(false);
      };
      recognition.onend = () => setIsRecording(false);
    }
  }, []);

  const addMessageToChat = (text: string, sender: 'user' | 'bot' | 'system', audioUrl?: string, groundingChunks?: GroundingChunk[]) => {
    const newMessage: ChatMessage = { id: Date.now().toString(), text, sender, audioUrl, timestamp: Date.now() };
    if (sender === 'bot' && groundingChunks) { (newMessage as any).groundingChunks = groundingChunks; }
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = async () => {
    const trimmedInput = inputText.trim();
    if (!trimmedInput && !isLoading) return;

    addMessageToChat(trimmedInput, 'user');
    setInputText('');
    setIsLoading(true);

    try {
      const knowledgeContextParts: Part[] = [];
      let textualContext = "";

      const relevantKnowledgeItems = knowledgeItems.filter(item => selectedKnowledgeIds.has(item.id));

      for (const item of relevantKnowledgeItems) {
        if (item.type === KnowledgeItemType.AUDIO_FILE && item.audioBase64 && item.fileMimeType) {
          knowledgeContextParts.push({ inlineData: { data: item.audioBase64, mimeType: item.fileMimeType } });
          textualContext += `Contexto de Áudio (Nome: ${item.name}): ${item.content}\n\n`; // item.content here is the description
        } else if (item.type === KnowledgeItemType.WEB_URL) {
          textualContext += `Nome do Conhecimento: ${item.name}\nTipo: URL Web\nURL: ${item.content}\n\n---\n\n`;
        } else if (item.content) { 
          textualContext += `Nome do Conhecimento: ${item.name}\nTipo: ${item.type}\nConteúdo: ${item.content}\n\n---\n\n`;
        }
      }
      
      if (textualContext.length > MAX_KNOWLEDGE_CONTEXT_LENGTH) {
        textualContext = textualContext.substring(0, MAX_KNOWLEDGE_CONTEXT_LENGTH) + "... (contexto textual truncado)\n\n---\n\n";
      }

      const queryParts: Part[] = [];
      if (textualContext) {
        const systemProvidedContextPrefix = `Use o seguinte contexto da Base de Conhecimento para responder à pergunta. Se a informação não estiver no contexto, afirme explicitamente que a resposta não foi encontrada no contexto fornecido.\nContexto da Base de Conhecimento:\n${textualContext}`;
        queryParts.push({ text: systemProvidedContextPrefix });
      }
      
      queryParts.push(...knowledgeContextParts); 
      queryParts.push({ text: `Pergunta do Usuário: ${trimmedInput}` });
      
      const geminiHistoryForNewChat = convertAppMessagesToGeminiHistory(messages);
      const { response: geminiResponse } = await sendMessageToGemini(queryParts, currentSystemInstructionInChat, geminiHistoryForNewChat);
      const botText = geminiResponse.text;
      
      let groundingData: GroundingChunk[] | undefined = undefined;
      if (geminiResponse.candidates && geminiResponse.candidates[0]?.groundingMetadata?.groundingChunks) {
        groundingData = geminiResponse.candidates[0].groundingMetadata.groundingChunks as GroundingChunk[];
      }

      addMessageToChat(botText, 'bot', undefined, groundingData);
      
      if (audioOutputEnabled && botText) {
        try { await textToSpeech(botText); } 
        catch (ttsError: any) {
            console.error("Falha ao reproduzir áudio:", ttsError);
            addMessageToChat(`Falha ao reproduzir áudio: ${ttsError.message || ttsError}`, 'system');
        }
      }

    } catch (error: any) {
      console.error("Erro ao enviar mensagem:", error);
      addMessageToChat(error.message || "Desculpe, ocorreu um erro.", 'system');
    } finally {
      setIsLoading(false);
      inputRef.current?.focus();
    }
  };
  
  const handleApplySystemInstruction = () => {
    setCurrentSystemInstructionInChat(systemInstruction);
    const geminiHistory = convertAppMessagesToGeminiHistory(messages);
    initializeChat(systemInstruction, geminiHistory);
    addMessageToChat(`Instrução do sistema atualizada e chat reiniciado.`, 'system');
  };

  const handleToggleRecording = () => {
    if (!SpeechRecognition) { addMessageToChat("Reconhecimento de voz não suportado.", 'system'); return; }
    if (isRecording) { recognition.stop(); } else { recognition.start(); }
    setIsRecording(!isRecording);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setNewKnowledgeFile(event.target.files[0]);
    } else {
      setNewKnowledgeFile(null);
    }
  };

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
      reader.readAsText(file);
    });
  };

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve((reader.result as string).split(',')[1]); 
      reader.onerror = (error) => reject(error);
      reader.readAsDataURL(file);
    });
  };

  const extractTextFromPdf = async (file: File): Promise<string> => {
    if (!ensurePdfJsWorkerInitialized()) {
      const errorMsg = "[App.tsx] extractTextFromPdf: PDF.js library not ready or worker could not be initialized.";
      console.error(errorMsg);
      addMessageToChat("Erro crítico: A biblioteca PDF não está configurada corretamente. Verifique o console.", 'system');
      throw new Error("Biblioteca de processamento de PDF não carregada ou mal configurada.");
    }
    const pdfjsLib = (window as any).pdfjsLib; 
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      let fullText = '';
      for (let i = 1; i <= pdfDoc.numPages; i++) {
        const page = await pdfDoc.getPage(i);
        const textContent = await page.getTextContent();
        fullText += textContent.items.map((item: any) => item.str).join(' ') + '\n';
      }
      return fullText;
    } catch (error) {
      console.error("[App.tsx] extractTextFromPdf: Error during PDF processing for file " + file.name + ":", error);
      const specificErrorMessage = error instanceof Error ? error.message : String(error);
      addMessageToChat(`Erro ao processar PDF (${file.name}): ${specificErrorMessage}. Verifique o console.`, 'system');
      throw error; 
    }
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;  
    }
  }

  const handleAddKnowledgeItem = async () => {
    if (!newKnowledgeName.trim()) {
      alert("Nome do Conhecimento não pode estar vazio.");
      return;
    }
    setIsFileProcessing(true);
    let itemContent = newKnowledgeContent; // For TEXT, WEB_CONTENT_PASTED, WEB_URL, AUDIO_FILE (description)
    let audioBase64Data: string | undefined = undefined;
    let fileMimeTypeData: string | undefined = undefined;
    let originalFileNameData: string | undefined = undefined;

    try {
      if (newKnowledgeType === KnowledgeItemType.WEB_URL) {
        if (!itemContent.trim() || !isValidUrl(itemContent)) {
          alert("Por favor, insira uma URL válida.");
          setIsFileProcessing(false);
          return;
        }
        // itemContent already holds the URL from newKnowledgeContent state
      } else if (newKnowledgeFile) {
        originalFileNameData = newKnowledgeFile.name;
        fileMimeTypeData = newKnowledgeFile.type;
        switch (newKnowledgeType) {
          case KnowledgeItemType.TXT_FILE:
          case KnowledgeItemType.MD_FILE:
            itemContent = await readFileAsText(newKnowledgeFile);
            break;
          case KnowledgeItemType.PDF_FILE:
             if (!pdfLibraryAvailable) {
                alert("A biblioteca PDF não está pronta. Por favor, aguarde ou verifique o console para erros.");
                setIsFileProcessing(false);
                return;
            }
            itemContent = await extractTextFromPdf(newKnowledgeFile);
            break;
          case KnowledgeItemType.AUDIO_FILE:
            audioBase64Data = await readFileAsBase64(newKnowledgeFile);
            if (!newKnowledgeContent.trim()) { // newKnowledgeContent is used for description here
                alert("Por favor, forneça uma breve descrição para o arquivo de áudio no campo 'Conteúdo/Descrição/URL'.");
                setIsFileProcessing(false);
                return;
            }
            // itemContent (for description) is already set from newKnowledgeContent
            break;
        }
      } else if ( // Check if file is required but not provided
        newKnowledgeType === KnowledgeItemType.TXT_FILE ||
        newKnowledgeType === KnowledgeItemType.MD_FILE ||
        newKnowledgeType === KnowledgeItemType.PDF_FILE ||
        newKnowledgeType === KnowledgeItemType.AUDIO_FILE
      ) {
        alert("Por favor, selecione um arquivo.");
        setIsFileProcessing(false);
        return;
      }
      
      // General content empty check (excluding AUDIO_FILE where content is description and file is primary)
      if (!itemContent.trim() && newKnowledgeType !== KnowledgeItemType.AUDIO_FILE && newKnowledgeType !== KnowledgeItemType.WEB_URL ) {
         alert("Conteúdo do conhecimento não pode estar vazio.");
         setIsFileProcessing(false);
         return;
      }


      const newItem: KnowledgeItem = {
        id: Date.now().toString(),
        name: newKnowledgeName,
        type: newKnowledgeType,
        content: itemContent, 
        createdAt: Date.now(),
        audioBase64: audioBase64Data,
        fileMimeType: fileMimeTypeData,
        originalFileName: originalFileNameData,
      };
      setKnowledgeItems(prev => [...prev, newItem]);
      setNewKnowledgeName('');
      setNewKnowledgeContent('');
      setNewKnowledgeFile(null);
      if(fileInputRef.current) fileInputRef.current.value = ""; 
      setShowKnowledgeForm(false);
    } catch (error: any) {
      console.error("Erro ao processar ou adicionar item de conhecimento:", error);
      if (!String(error.message).includes("Erro ao processar PDF") && !String(error.message).includes("Biblioteca de processamento de PDF")) {
        alert(`Falha ao adicionar conhecimento: ${error.message || 'Erro desconhecido'}`);
      }
    } finally {
      setIsFileProcessing(false);
    }
  };

  const handleDeleteKnowledgeItem = (id: string) => {
    setKnowledgeItems(prev => prev.filter(item => item.id !== id));
    setSelectedKnowledgeIds(prev => { const newSet = new Set(prev); newSet.delete(id); return newSet; });
  };

  const handleToggleKnowledgeSelection = (id: string) => {
    setSelectedKnowledgeIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) newSet.delete(id); else newSet.add(id);
      return newSet;
    });
  };
  
  const handleResetChat = async () => {
    setMessages([]);
    initializeChat(currentSystemInstructionInChat, []);
    addMessageToChat("Chat reiniciado.", 'system');
  };

  const requiresFileInput = [
    KnowledgeItemType.TXT_FILE, 
    KnowledgeItemType.MD_FILE, 
    KnowledgeItemType.PDF_FILE, 
    KnowledgeItemType.AUDIO_FILE
  ].includes(newKnowledgeType);

  // Content input field: textarea for TEXT/WEB_CONTENT_PASTED, input type="url" for WEB_URL, textarea for AUDIO_FILE (description)
  const requiresContentInput = [
    KnowledgeItemType.TEXT,
    KnowledgeItemType.WEB_CONTENT_PASTED,
    KnowledgeItemType.AUDIO_FILE, // For description
    KnowledgeItemType.WEB_URL // For URL input
  ].includes(newKnowledgeType);

  const getContentInputType = () => {
    if (newKnowledgeType === KnowledgeItemType.WEB_URL) return 'url';
    return 'text'; // Default, rendered as textarea
  }

  const getContentPlaceholder = () => {
    switch(newKnowledgeType) {
      case KnowledgeItemType.AUDIO_FILE: return "Descrição textual do áudio (obrigatório se arquivo de áudio selecionado)";
      case KnowledgeItemType.WEB_URL: return "https://exemplo.com/artigo";
      case KnowledgeItemType.TEXT:
      case KnowledgeItemType.WEB_CONTENT_PASTED:
      default: return "Conteúdo textual...";
    }
  }


  const getFileInputAcceptType = () => {
    switch(newKnowledgeType) {
      case KnowledgeItemType.TXT_FILE: return ".txt";
      case KnowledgeItemType.MD_FILE: return ".md";
      case KnowledgeItemType.PDF_FILE: return ".pdf";
      case KnowledgeItemType.AUDIO_FILE: return "audio/*";
      default: return "";
    }
  }

  const baseInputStyle = "w-full p-2.5 bg-white border border-orange-300 rounded-lg text-gray-800 placeholder-gray-400 focus:ring-orange-500 focus:border-orange-500";
  const baseButtonStyle = "font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-all duration-150 ease-in-out shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-opacity-50";
  const primaryButtonStyle = `${baseButtonStyle} text-white bg-orange-500 hover:bg-orange-600 focus:ring-orange-400`;
  const secondaryButtonStyle = `${baseButtonStyle} text-white bg-orange-600 hover:bg-orange-700 focus:ring-orange-500`;
  const destructiveButtonStyle = `${baseButtonStyle} text-white bg-red-500 hover:bg-red-600 focus:ring-red-400`;

  return (
    <div className="flex flex-col h-screen max-h-screen bg-white text-gray-800">
      <header className="bg-orange-500 text-white p-4 shadow-lg sticky top-0 z-10">
        <div className="flex items-center justify-center space-x-4">
          <img
            src="/logo2ia.png"
            alt="2ia Logo"
            className="h-10 w-auto"
          />
          <h1 className="text-3xl font-bold tracking-tight">ChatBot 2ia</h1>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        <aside className="w-1/3 max-w-md bg-orange-50 p-6 overflow-y-auto border-r border-orange-200 flex flex-col space-y-8 shadow-xl">
          <section>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-semibold text-orange-600">Base de Conhecimento</h2>
              <button
                onClick={() => setShowKnowledgeForm(!showKnowledgeForm)}
                className="p-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors shadow-md hover:shadow-lg"
                aria-label={showKnowledgeForm ? "Cancelar Adição" : "Adicionar Conhecimento"}
              >
                <PlusIcon className="w-6 h-6" />
              </button>
            </div>

            {showKnowledgeForm && (
              <div className="bg-white border border-orange-200 p-4 rounded-xl shadow-lg mb-6 space-y-4">
                <input 
                  type="text" 
                  placeholder="Nome do Conhecimento (ex: Artigo sobre IA)" 
                  value={newKnowledgeName} 
                  onChange={(e) => setNewKnowledgeName(e.target.value)}
                  className={baseInputStyle}
                  aria-label="Nome do Conhecimento"
                />
                <select 
                  value={newKnowledgeType} 
                  onChange={(e) => {
                    setNewKnowledgeType(e.target.value as KnowledgeItemType);
                    setNewKnowledgeFile(null); 
                    setNewKnowledgeContent(''); 
                    if(fileInputRef.current) fileInputRef.current.value = "";
                  }}
                  className={`${baseInputStyle} appearance-none`}
                  aria-label="Tipo do Conhecimento"
                >
                  {Object.values(KnowledgeItemType).map(type => (
                    <option key={type} value={type} className="bg-slate-700 text-slate-100">{type}</option>
                  ))}
                </select>

                {requiresFileInput && (
                  <input 
                    type="file"
                    ref={fileInputRef}
                    key={newKnowledgeType} // Re-mount when type changes to clear file
                    onChange={handleFileChange}
                    accept={getFileInputAcceptType()}
                    className={`${baseInputStyle} file:mr-3 file:py-2 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-orange-500 file:text-white hover:file:bg-orange-600 cursor-pointer`}
                    aria-label="Selecionar Arquivo"
                  />
                )}
                
                {requiresContentInput && (
                  newKnowledgeType === KnowledgeItemType.WEB_URL ? (
                    <input
                      type="url"
                      placeholder={getContentPlaceholder()}
                      value={newKnowledgeContent}
                      onChange={(e) => setNewKnowledgeContent(e.target.value)}
                      className={baseInputStyle}
                      aria-label="URL do Conteúdo"
                    />
                  ) : (
                    <textarea 
                      placeholder={getContentPlaceholder()}
                      value={newKnowledgeContent} 
                      onChange={(e) => setNewKnowledgeContent(e.target.value)}
                      rows={newKnowledgeType === KnowledgeItemType.AUDIO_FILE ? 2 : 4}
                      className={baseInputStyle}
                      aria-label={newKnowledgeType === KnowledgeItemType.AUDIO_FILE ? "Descrição do Áudio" : "Conteúdo do Conhecimento"}
                    />
                  )
                )}

                 { newKnowledgeType === KnowledgeItemType.PDF_FILE && newKnowledgeFile &&
                    <p className="text-xs text-gray-500">O texto será extraído do PDF. Arquivos grandes podem levar alguns segundos.</p> }
                 { newKnowledgeType === KnowledgeItemType.AUDIO_FILE &&
                    <p className="text-xs text-gray-500">O arquivo de áudio será enviado ao Gemini se este item for selecionado como contexto. A descrição é para sua referência e é obrigatória.</p> }
                 { newKnowledgeType === KnowledgeItemType.WEB_URL &&
                    <p className="text-xs text-gray-500">A URL fornecida será incluída no contexto enviado ao Gemini.</p> }
                
                <button 
                  onClick={handleAddKnowledgeItem}
                  disabled={isFileProcessing || (newKnowledgeType === KnowledgeItemType.PDF_FILE && !pdfLibraryAvailable)}
                  className={`${secondaryButtonStyle} w-full flex items-center justify-center disabled:bg-slate-500 disabled:hover:bg-slate-500 disabled:cursor-not-allowed`}
                >
                  {isFileProcessing ? <SpinnerIcon className="w-5 h-5 mr-2" /> : null}
                  {isFileProcessing ? 'Processando...' : 
                    (newKnowledgeType === KnowledgeItemType.PDF_FILE && !pdfLibraryAvailable) ? 'Biblioteca PDF não carregada' : 'Adicionar Conhecimento'
                  }
                </button>
              </div>
            )}
            
            {knowledgeItems.length > 0 && !showKnowledgeForm && (
              <p className="text-sm text-gray-600 mb-3 px-1">
                {selectedKnowledgeIds.size === 0
                  ? "Clique em um item para usá-lo como contexto."
                  : `${selectedKnowledgeIds.size} item(ns) selecionado(s) para contexto.`}
              </p>
            )}
            <div className="space-y-3 max-h-[28rem] overflow-y-auto pr-1 custom-scrollbar">
              {knowledgeItems.length === 0 && !showKnowledgeForm && (
                <p className="text-gray-500 text-center py-6">Nenhum item de conhecimento adicionado.</p>
              )}
              {knowledgeItems.slice().reverse().map(item => (
                <KnowledgeItemCard 
                  key={item.id} 
                  item={item} 
                  onDelete={handleDeleteKnowledgeItem}
                  onSelect={handleToggleKnowledgeSelection}
                  isSelected={selectedKnowledgeIds.has(item.id)}
                />
              ))}
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-orange-600 mb-4">Configurações</h2>
            <div className="bg-white border border-orange-200 p-4 rounded-xl shadow-lg space-y-5">
              <div>
                <label htmlFor="systemInstruction" className="block text-sm font-medium text-gray-700 mb-1.5">Instrução do Sistema:</label>
                <textarea 
                  id="systemInstruction" value={systemInstruction} 
                  onChange={(e) => setSystemInstruction(e.target.value)}
                  rows={4}
                  className={`${baseInputStyle} text-sm`}
                  placeholder={DEFAULT_SYSTEM_INSTRUCTION}
                />
                <button
                  onClick={handleApplySystemInstruction}
                  className={`${primaryButtonStyle} mt-3 w-full text-sm`}
                >
                  Aplicar Instrução e Reiniciar Chat
                </button>
              </div>
              <div className="flex items-center justify-between pt-2">
                <span className="text-sm font-medium text-gray-700">Saída de Áudio</span>
                <button
                  onClick={() => setAudioOutputEnabled(!audioOutputEnabled)}
                  className={`p-2.5 rounded-full transition-colors duration-200 ${audioOutputEnabled ? 'bg-orange-500 hover:bg-orange-600' : 'bg-gray-400 hover:bg-gray-500'}`}
                  aria-pressed={audioOutputEnabled}
                  aria-label={audioOutputEnabled ? "Desativar saída de áudio" : "Ativar saída de áudio"}
                >
                  <SpeakerIcon enabled={audioOutputEnabled} className="w-5 h-5 text-white" />
                </button>
              </div>
               <button
                onClick={handleResetChat}
                className={`${destructiveButtonStyle} w-full text-sm`}
              >
                Limpar Histórico do Chat
              </button>
            </div>
          </section>
        </aside>

        <main className="flex-1 flex flex-col bg-gray-50">
          <div className="flex-1 p-4 sm:p-6 space-y-4 overflow-y-auto chat-messages custom-scrollbar" ref={chatMessagesEndRef} aria-live="polite">
            {messages.map((msg) => <ChatMessageItem key={msg.id} message={msg} groundingChunks={(msg as any).groundingChunks} />)}
            {isLoading && messages[messages.length -1]?.sender === 'user' && (
              <div className="flex justify-start mb-4">
                <div className="p-3 rounded-lg chat-bubble-bot self-start shadow-md">
                  <SpinnerIcon className="w-5 h-5 text-slate-300 inline mr-2" /> 
                  <span className="italic">Digitando...</span>
                </div>
              </div>
            )}
          </div>

          <div className="bg-white p-3 sm:p-4 border-t border-orange-200 shadow-top sticky bottom-0">
            <div className="flex items-center space-x-2 sm:space-x-3">
              {SpeechRecognition && (
                <button
                  onClick={handleToggleRecording}
                  disabled={isLoading}
                  className={`p-3 rounded-full transition-colors duration-150 ${isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : 'bg-orange-500 hover:bg-orange-600'} text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-md`}
                  aria-pressed={isRecording}
                  aria-label={isRecording ? "Parar Gravação" : "Iniciar Gravação"}
                >
                  <MicIcon isRecording={isRecording} className="w-6 h-6" />
                </button>
              )}
              <input
                ref={inputRef} type="text" value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !isLoading && handleSendMessage()}
                placeholder={isRecording ? "Ouvindo..." : "Digite sua mensagem..."}
                className="flex-1 p-3 bg-white border border-orange-300 rounded-lg text-gray-800 placeholder-gray-400 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-70"
                disabled={isLoading || isRecording}
              />
              <button
                onClick={handleSendMessage}
                disabled={isLoading || !inputText.trim()}
                className={`${primaryButtonStyle} p-3 disabled:bg-gray-400 disabled:hover:bg-gray-400 disabled:cursor-not-allowed`}
                aria-label="Enviar Mensagem"
              >
                {isLoading ? <SpinnerIcon className="w-6 h-6" /> : <SendIcon className="w-6 h-6" />}
              </button>
            </div>
             {!process.env.API_KEY && (
                <p className="text-xs text-red-400 mt-2 text-center font-semibold" role="alert">
                  <strong>Atenção:</strong> A API_KEY não está configurada. O chatbot não funcionará.
                </p>
              )}
          </div>
        </main>
      </div>
    </div>
  );
};
export default App;
