import * as core from "zod/v4/core";
import * as schemas from "./schemas.js";
export declare function string<T = unknown>(params?: string | core.$ZodStringParams): schemas.ZodMiniString<T>;
export declare function number<T = unknown>(params?: string | core.$ZodNumberParams): schemas.ZodMiniNumber<T>;
export declare function boolean<T = unknown>(params?: string | core.$ZodBooleanParams): schemas.ZodMiniBoolean<T>;
export declare function bigint<T = unknown>(params?: string | core.$ZodBigIntParams): schemas.ZodMiniBigInt<T>;
export declare function date<T = unknown>(params?: string | core.$ZodDateParams): schemas.ZodMiniDate<T>;
