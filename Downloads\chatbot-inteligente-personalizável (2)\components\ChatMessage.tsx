
import React from 'react';
import { ChatMessage, GroundingChunk } from '../types';

interface ChatMessageProps {
  message: ChatMessage;
  groundingChunks?: GroundingChunk[];
}

const ChatMessageItem: React.FC<ChatMessageProps> = ({ message, groundingChunks }) => {
  const isUser = message.sender === 'user';
  const bubbleClass = isUser 
    ? 'chat-bubble-user self-end rounded-l-xl rounded-tr-xl' 
    : 'chat-bubble-bot self-start rounded-r-xl rounded-tl-xl';
  const alignClass = isUser ? 'items-end' : 'items-start';

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
  };

  const renderTextWithLinks = (text: string) => {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.split(urlRegex).map((part, index) => {
      if (part.match(urlRegex)) {
        return <a key={index} href={part} target="_blank" rel="noopener noreferrer" className="text-orange-500 underline hover:text-orange-600 transition-colors">{part}</a>;
      }
      return part;
    });
  };

  return (
    <div className={`flex flex-col mb-5 ${alignClass} w-full group`}>
      <div className={`py-3 px-4 rounded-lg max-w-[85%] sm:max-w-[75%] break-words shadow-lg ${bubbleClass}`}>
        {message.sender === 'system' ? (
          <p className="text-xs italic text-gray-500">{message.text}</p>
        ) : (
          <p className="whitespace-pre-wrap text-base">{renderTextWithLinks(message.text)}</p>
        )}
         {message.audioUrl && (
          <audio controls src={message.audioUrl} className="mt-2.5 w-full h-10 filter brightness-125"></audio>
        )}
      </div>
      <span className={`text-xs mt-1.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${isUser ? 'text-gray-500 self-end mr-1' : 'text-gray-500 self-start ml-1'}`}>
        {formatTime(message.timestamp)}
      </span>
      {groundingChunks && groundingChunks.length > 0 && message.sender === 'bot' && (
        <div className="mt-2 text-xs text-gray-700 self-start ml-1 p-3 bg-orange-50 border border-orange-200 rounded-lg shadow max-w-[85%] sm:max-w-[75%]">
          <p className="font-semibold mb-1.5 text-orange-600">Fontes:</p>
          <ul className="list-disc list-inside space-y-1">
            {groundingChunks.map((chunk, index) => {
              const source = chunk.web || chunk.retrievedContext;
              if (source && source.uri) {
                return (
                  <li key={index}>
                    <a href={source.uri} target="_blank" rel="noopener noreferrer" className="text-orange-500 hover:underline hover:text-orange-600 transition-colors">
                      {source.title || source.uri}
                    </a>
                  </li>
                );
              }
              return null;
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ChatMessageItem;
