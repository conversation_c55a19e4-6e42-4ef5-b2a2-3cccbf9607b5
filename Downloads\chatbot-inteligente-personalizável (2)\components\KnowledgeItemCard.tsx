
import React from 'react';
import { KnowledgeItem } from '../types';
import TrashIcon from './icons/TrashIcon';

interface KnowledgeItemCardProps {
  item: KnowledgeItem;
  onDelete: (id: string) => void;
  onSelect?: (id: string) => void;
  isSelected?: boolean;
}

const KnowledgeItemCard: React.FC<KnowledgeItemCardProps> = ({ item, onDelete, onSelect, isSelected }) => {
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('pt-BR', { dateStyle: 'short', timeStyle: 'short'});
  };

  return (
    <div
      className={`bg-white border p-4 rounded-xl shadow-lg mb-3 transition-all duration-200 hover:shadow-xl hover:bg-orange-50
                  ${isSelected ? 'ring-2 ring-orange-500 border-orange-300' : 'border-gray-200'}
                  ${onSelect ? 'cursor-pointer' : ''}`}
      onClick={onSelect ? () => onSelect(item.id) : undefined}
      aria-selected={isSelected}
      role="button"
      tabIndex={onSelect ? 0 : -1}
      onKeyDown={onSelect ? (e) => (e.key === 'Enter' || e.key === ' ') && onSelect(item.id) : undefined}
    >
      <div className="flex justify-between items-start mb-2">
        <div>
          <h4 className="font-semibold text-orange-600 break-all text-base">{item.name}</h4>
          <span className="text-xs text-gray-500 uppercase tracking-wider">{item.type}</span>
        </div>
        <button
          onClick={(e) => { e.stopPropagation(); onDelete(item.id); }}
          className="text-gray-400 hover:text-red-500 transition-colors p-1.5 rounded-full hover:bg-red-500/10"
          aria-label={`Excluir item de conhecimento: ${item.name}`}
        >
          <TrashIcon className="w-5 h-5" />
        </button>
      </div>
      <p className="text-sm text-gray-700 max-h-24 overflow-y-auto break-words whitespace-pre-wrap custom-scrollbar pr-1">{item.content.substring(0,150)}{item.content.length > 150 ? "..." : ""}</p>
      <p className="text-xs text-gray-500 mt-3 text-right">Criado em: {formatDate(item.createdAt)}</p>
    </div>
  );
};

export default KnowledgeItemCard;
