<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBot 2ia</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Inter', sans-serif;
        color: #374151; /* text-gray-700 */
        background-color: #ffffff; /* bg-white */
      }
      .chat-bubble-user {
        background-color: #ea580c; /* bg-orange-600 */
        color: white;
      }
      .chat-bubble-bot {
        background-color: #f9fafb; /* bg-gray-50 */
        color: #374151; /* text-gray-700 */
        border: 1px solid #fed7aa; /* border-orange-200 */
      }
      /* Custom scrollbar for chat and other elements */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #1e293b; /* bg-slate-800 */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb {
        background: #475569; /* bg-slate-600 */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #64748b; /* bg-slate-500 */
      }
      /* General focus style for accessibility and modern feel */
      input:focus, 
      textarea:focus, 
      select:focus, 
      button:focus-visible {
        outline: 2px solid #38bdf8; /* sky-400 */
        outline-offset: 2px;
        box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.3); /* Softer glow */
      }
      /* Remove default outline for buttons when focus-visible is supported */
      button:focus:not(:focus-visible) {
        outline: none;
      }
    </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.5.1"
  }
}
</script>
<!-- Load PDF.js library globally from jsDelivr -->
<script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.js"></script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-slate-900 text-slate-200">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>